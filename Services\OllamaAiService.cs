using System.Text;
using System.Text.Json;

namespace Whatsapp_Chatbot.Services
{
    public class OllamaAiService : IAiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiUrl = "http://localhost:11434/api/generate";

        public OllamaAiService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<string> AskGeminiAsync(string userMessage)
        {
            try
            {
                var requestBody = new
                {
                    model = "llama2", // You can change this to other models like "mistral", "codellama", etc.
                    prompt = userMessage,
                    stream = false
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_apiUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    
                    if (result.TryGetProperty("response", out var responseText))
                    {
                        return responseText.GetString() ?? "I'm here to help!";
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return "Ollama is not running. Please install and start Ollama first. Visit: https://ollama.ai";
                }
                
                return "I'm having trouble connecting to the local AI service.";
            }
            catch (HttpRequestException)
            {
                return "Ollama is not running. To use local AI:\n1. Install Ollama from https://ollama.ai\n2. Run 'ollama pull llama2'\n3. Start Ollama service";
            }
            catch (Exception ex)
            {
                return $"Error with local AI service: {ex.Message}";
            }
        }
    }
}
