﻿using Google.Cloud.AIPlatform.V1;
using Google.Protobuf.WellKnownTypes;
using Google.Api.Gax.ResourceNames;
using Google.Apis.Auth.OAuth2;

namespace Whatsapp_Chatbot.Services
{
    public class AiService : IAiService
    {
        private readonly string projectId = "whatsappchatbot-470020";
        private readonly string location = "us-central1";
        private readonly string credentialsPath;

        public AiService(IWebHostEnvironment environment)
        {
            credentialsPath = Path.Combine(environment.WebRootPath, "ai", "whatsappchatbot-470020-30ac9a4cf5d2.json");
        }

        public async Task<string> AskGeminiAsync(string userMessage)
        {
            try
            {
                // Set the credentials environment variable
                Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", credentialsPath);

                // Create the client
                PredictionServiceClient client = await PredictionServiceClient.CreateAsync();

                // Use Gemini Pro model instead of text-bison
                var model = $"projects/{projectId}/locations/{location}/publishers/google/models/gemini-1.5-flash-001";

                // Build the request for Gemini
                var instance = new Google.Protobuf.WellKnownTypes.Value
                {
                    StructValue = new Struct()
                };

                // Create the contents structure for Gemini
                var contents = new Google.Protobuf.WellKnownTypes.Value
                {
                    ListValue = new ListValue()
                };

                var content = new Google.Protobuf.WellKnownTypes.Value
                {
                    StructValue = new Struct()
                };

                content.StructValue.Fields["role"] = Google.Protobuf.WellKnownTypes.Value.ForString("user");

                var parts = new Google.Protobuf.WellKnownTypes.Value
                {
                    ListValue = new ListValue()
                };

                var part = new Google.Protobuf.WellKnownTypes.Value
                {
                    StructValue = new Struct()
                };
                part.StructValue.Fields["text"] = Google.Protobuf.WellKnownTypes.Value.ForString(userMessage);

                parts.ListValue.Values.Add(part);
                content.StructValue.Fields["parts"] = parts;
                contents.ListValue.Values.Add(content);

                instance.StructValue.Fields["contents"] = contents;

                // Add generation config for better responses
                var generationConfig = new Google.Protobuf.WellKnownTypes.Value
                {
                    StructValue = new Struct()
                };
                generationConfig.StructValue.Fields["temperature"] = Google.Protobuf.WellKnownTypes.Value.ForNumber(0.7);
                generationConfig.StructValue.Fields["maxOutputTokens"] = Google.Protobuf.WellKnownTypes.Value.ForNumber(1024);

                instance.StructValue.Fields["generation_config"] = generationConfig;

                // Make the request
                var request = new PredictRequest
                {
                    Endpoint = model,
                    Instances = { instance }
                };

                var response = await client.PredictAsync(request);

                // Extract the response
                if (response.Predictions.Count > 0)
                {
                    var prediction = response.Predictions[0];
                    if (prediction.StructValue.Fields.TryGetValue("candidates", out var candidates))
                    {
                        var candidatesList = candidates.ListValue.Values;
                        if (candidatesList.Count > 0)
                        {
                            var candidate = candidatesList[0];
                            if (candidate.StructValue.Fields.TryGetValue("content", out var content_response))
                            {
                                if (content_response.StructValue.Fields.TryGetValue("parts", out var parts_response))
                                {
                                    var partsList = parts_response.ListValue.Values;
                                    if (partsList.Count > 0)
                                    {
                                        var firstPart = partsList[0];
                                        if (firstPart.StructValue.Fields.TryGetValue("text", out var text))
                                        {
                                            return text.StringValue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                return "No response from AI.";
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }
    }
}
