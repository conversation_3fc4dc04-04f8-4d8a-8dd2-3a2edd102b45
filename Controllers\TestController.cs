using Microsoft.AspNetCore.Mvc;
using Whatsapp_Chatbot.Services;

namespace Whatsapp_Chatbot.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly IWhatsAppService _whatsAppService;

        public TestController(IWhatsAppService whatsAppService)
        {
            _whatsAppService = whatsAppService;
        }

        [HttpPost("send-text")]
        public async Task<IActionResult> SendTextMessage([FromBody] SendTextRequest request)
        {
            if (string.IsNullOrEmpty(request.Mobile) || string.IsNullOrEmpty(request.Message))
            {
                return BadRequest("Mobile number and message are required");
            }

            try
            {
                var result = await _whatsAppService.SendTextMessage(request.Mobile, request.Message);
                
                if (result)
                {
                    return Ok(new { success = true, message = "Text message sent successfully" });
                }
                else
                {
                    return StatusCode(500, new { success = false, message = "Failed to send text message" });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }
    }

    public class SendTextRequest
    {
        public string Mobile { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
