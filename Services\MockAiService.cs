namespace Whatsapp_Chatbot.Services
{
    public class MockAiService : IAiService
    {
        private readonly List<string> _responses = new()
        {
            "Hello! I'm a chatbot assistant. How can I help you today?",
            "That's an interesting question! Let me think about that...",
            "I understand what you're asking. Here's what I think:",
            "Thanks for your message! I'm here to help you.",
            "That's a great point! Let me provide some information about that.",
            "I appreciate you reaching out. How else can I assist you?",
            "Interesting! I'd be happy to help you with that.",
            "Thank you for your question. Here's my response:",
            "I see what you mean. Let me help you with that.",
            "That's a good question! I'm here to provide assistance."
        };

        public async Task<string> AskGeminiAsync(string userMessage)
        {
            // Simulate some processing time
            await Task.Delay(500);

            // Simple keyword-based responses
            var message = userMessage.ToLower();
            
            if (message.Contains("hello") || message.Contains("hi") || message.Contains("hey"))
            {
                return "Hello! Nice to meet you. How can I help you today?";
            }
            
            if (message.Contains("how are you") || message.Contains("how do you do"))
            {
                return "I'm doing great, thank you for asking! I'm here and ready to help you with any questions you might have.";
            }
            
            if (message.Contains("thank") || message.Contains("thanks"))
            {
                return "You're very welcome! I'm glad I could help. Is there anything else you'd like to know?";
            }
            
            if (message.Contains("bye") || message.Contains("goodbye") || message.Contains("see you"))
            {
                return "Goodbye! It was nice chatting with you. Feel free to reach out anytime you need help!";
            }
            
            if (message.Contains("help") || message.Contains("assist"))
            {
                return "I'm here to help! You can ask me questions and I'll do my best to provide useful responses. What would you like to know?";
            }
            
            if (message.Contains("name") || message.Contains("who are you"))
            {
                return "I'm an AI assistant chatbot designed to help answer your questions and provide assistance. What can I help you with?";
            }

            // For other messages, return a random response with the user's message context
            var random = new Random();
            var randomResponse = _responses[random.Next(_responses.Count)];
            
            return $"{randomResponse} You mentioned: \"{userMessage}\". How can I help you further with this?";
        }
    }
}
