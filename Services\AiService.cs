﻿using Google.Cloud.AIPlatform.V1;
using Google.Protobuf.WellKnownTypes;

namespace Whatsapp_Chatbot.Services
{
    public class AiService : IAiService
    {
        private readonly string projectId = "whatsappchatbot-470020";
        private readonly string location = "us-central1";

        public async Task<string> AskGeminiAsync(string userMessage)
        {
            // Create the client (it will use the JSON key from GOOGLE_APPLICATION_CREDENTIALS)
            PredictionServiceClient client = await PredictionServiceClient.CreateAsync();

            // The full model name
            var model = $"projects/{projectId}/locations/{location}/publishers/google/models/text-bison-001";

            // Build the instance using fully qualified Value type
            var instance = new Google.Protobuf.WellKnownTypes.Value
            {
                StructValue = new Struct()
            };
            instance.StructValue.Fields["content"] = Google.Protobuf.WellKnownTypes.Value.ForString(userMessage);

            // Make the request
            var response = await client.PredictAsync(model, new[] { instance });

            // Get the first prediction
            if (response.Predictions.Count > 0)
            {
                var prediction = response.Predictions[0].StructValue.Fields["content"].StringValue;
                return prediction;
            }

            return "No response from AI.";
