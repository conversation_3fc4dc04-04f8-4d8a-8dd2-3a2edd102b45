using Whatsapp_Chatbot.Services;
using Whatsapp_Chatbot.Settings;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

builder.Services.AddSwaggerGen();

// Add HttpClient for AI services
builder.Services.AddHttpClient();

builder.Services.AddTransient<IWhatsAppService, WhatsAppService>();

// AI Service Options (choose one by uncommenting):
// 1. Mock AI Service (for testing) - Currently active
//builder.Services.AddTransient<IAiService, MockAiService>();

// 2. Hugging Face (completely free)
 builder.Services.AddTransient<IAiService, HuggingFaceAiService>();

// 3. OpenAI (free $5 credit)
// builder.Services.AddTransient<IAiService, OpenAiService>();

// 4. <PERSON><PERSON><PERSON> (free local AI)
// builder.Services.AddTransient<IAiService, OllamaAiService>();

// 5. Google Gemini (requires billing setup)
// builder.Services.AddTransient<IAiService, AiService>();

builder.Services.Configure<WhatsAppSettings>(builder.Configuration.GetSection(nameof(WhatsAppSettings)));

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{

}
app.MapOpenApi();

app.UseSwagger();
app.UseSwaggerUI();

app.UseCors(op =>
{
    op.AllowAnyOrigin()
      .AllowAnyMethod()
      .AllowAnyHeader();
});


//app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();
