﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Text.Json;
using Whatsapp_Chatbot.Services;

[ApiController]
[Route("webhook")]
public class WebhookController : ControllerBase
{
    private readonly IAiService _aiService;
    private readonly IWhatsAppService _whatsAppService;

    public WebhookController(IAiService aiService, IWhatsAppService whatsAppService)
    {
        _aiService = aiService;
        _whatsAppService = whatsAppService;
    }

    // STEP 1: Verification
    [HttpGet]
    public IActionResult Verify([FromQuery(Name = "hub.mode")] string hubMode,
                                [FromQuery(Name = "hub.challenge")] string hubChallenge,
                                [FromQuery(Name = "hub.verify_token")] string hubVerifyToken)
    {
        const string VERIFY_TOKEN = "myverifytoken"; // use the same one you set in Meta portal

        if (hubMode == "subscribe" && hubVerifyToken == VERIFY_TOKEN)
        {
            return Ok(hubChallenge);
        }

        return Unauthorized();
    }

    // STEP 2: Receive messages
    //[HttpPost]
    //[AllowAnonymous]
    //public IActionResult Receive([FromBody] JObject body)
    //{
    //    Console.WriteLine(body.ToString());

    //    // Example: Extract the message text
    //    var messages = body["entry"]?[0]?["changes"]?[0]?["value"]?["messages"];
    //    if (messages != null)
    //    {
    //        var text = messages[0]?["text"]?["body"]?.ToString();
    //        var from = messages[0]?["from"]?.ToString();

    //        Console.WriteLine($"New message from {from}: {text}");
    //    }

    //    return Ok();
    //}

    [HttpPost()]
    [AllowAnonymous]
    public async Task<IActionResult> Receive( JsonElement payload)
    {
        try
        {
            // Make sure the payload has the expected structure
            var entry = payload.GetProperty("entry")[0];
            var changes = entry.GetProperty("changes")[0];
            var value = changes.GetProperty("value");

            // Sometimes "messages" might not exist (like status updates)
            if (value.TryGetProperty("messages", out JsonElement messages))
            {
                var messageObj = messages[0];

                // Get the message text
                string? messageText = messageObj.GetProperty("text")
                                               .GetProperty("body")
                                               .GetString();

                // Get the user phone number (who sent the message)
                string? fromNumber = messageObj.GetProperty("from").GetString();

                if (string.IsNullOrEmpty(messageText) || string.IsNullOrEmpty(fromNumber))
                {
                    Console.WriteLine("Message text or from number is null/empty");
                    return Ok();
                }

                Console.WriteLine($"Message from {fromNumber}: {messageText}");
                var messagwForAi =$"act as chatbot and choose one of these whatsapp template due to user message" +
                    $"user message: {messageText} " +
                    "templates: hello_world : Hello World\r\nWelcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.\r\nWhatsApp Business Platform sample message ," +
                    " employee_number : Emplyee Number\r\nYour Employee Number Is 502\r\nyos\r\n ," +
                    "first : hi there\r\ntest test tessss testt\r\nfffffff ";
                // Get AI response
                string aiResponse = await _aiService.AskGeminiAsync(messageText);
                Console.WriteLine($"AI Response: {aiResponse}");
                // Send AI response back to user via WhatsApp using text message (no template needed)
                var result = await _whatsAppService.SendTextMessage(fromNumber, aiResponse);

                if (result)
                {
                    Console.WriteLine($"Successfully sent AI response to {fromNumber}");
                }
                else
                {
                    Console.WriteLine($"Failed to send AI response to {fromNumber}");
                }
            }
            else
            {
                Console.WriteLine("No messages in this webhook call.");
            }

            return Ok();
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error reading payload: " + ex.Message);
            return BadRequest();
        }
    }

}
