<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Google.Apis.Aiplatform.v1" Version="1.70.0.3880" />
    <PackageReference Include="Google.Cloud.AIPlatform.V1" Version="3.45.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="9.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="9.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.3" />
  </ItemGroup>

</Project>
