using System.Text;
using System.Text.Json;

namespace Whatsapp_Chatbot.Services
{
    public class HuggingFaceAiService : IAiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiUrl = "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium";
        
        public HuggingFaceAiService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<string> AskGeminiAsync(string userMessage)
        {
            try
            {
                var requestBody = new
                {
                    inputs = userMessage,
                    parameters = new
                    {
                        max_length = 100,
                        temperature = 0.7,
                        do_sample = true
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_apiUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement[]>(responseContent);
                    
                    if (result.Length > 0 && result[0].TryGetProperty("generated_text", out var generatedText))
                    {
                        var fullText = generatedText.GetString();
                        // Remove the original input from the response
                        var aiResponse = fullText?.Replace(userMessage, "").Trim();
                        return string.IsNullOrEmpty(aiResponse) ? "I understand your message. How can I help you further?" : aiResponse;
                    }
                }
                
                return "I'm here to help! Could you please rephrase your question?";
            }
            catch (Exception ex)
            {
                return $"I'm having trouble processing your request right now. Error: {ex.Message}";
            }
        }
    }
}
