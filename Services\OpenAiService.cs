using System.Text;
using System.Text.Json;

namespace Whatsapp_Chatbot.Services
{
    public class OpenAiService : IAiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly string _apiUrl = "https://api.openai.com/v1/chat/completions";

        public OpenAiService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _apiKey = configuration["OpenAI:ApiKey"] ?? "";
            
            if (!string.IsNullOrEmpty(_apiKey))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _apiKey);
            }
        }

        public async Task<string> AskGeminiAsync(string userMessage)
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                return "OpenAI API key not configured. Please add your API key to appsettings.json";
            }

            try
            {
                var requestBody = new
                {
                    model = "gpt-3.5-turbo",
                    messages = new[]
                    {
                        new { role = "system", content = "You are a helpful assistant for a WhatsApp chatbot. Keep responses concise and friendly." },
                        new { role = "user", content = userMessage }
                    },
                    max_tokens = 150,
                    temperature = 0.7
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_apiUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    
                    if (result.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var content_prop))
                        {
                            return content_prop.GetString() ?? "I'm here to help!";
                        }
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return $"OpenAI API error: {response.StatusCode} - {errorContent}";
                }
                
                return "I'm having trouble generating a response right now.";
            }
            catch (Exception ex)
            {
                return $"Error connecting to OpenAI: {ex.Message}";
            }
        }
    }
}
