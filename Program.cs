using Whatsapp_Chatbot.Services;
using Whatsapp_Chatbot.Settings;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

builder.Services.AddSwaggerGen();

builder.Services.AddTransient<IWhatsAppService, WhatsAppService>();
builder.Services.AddTransient<IAiService, AiService>();

builder.Services.Configure<WhatsAppSettings>(builder.Configuration.GetSection(nameof(WhatsAppSettings)));

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{

}
app.MapOpenApi();

app.UseSwagger();
app.UseSwaggerUI();

app.UseCors(op =>
{
    op.AllowAnyOrigin()
      .AllowAnyMethod()
      .AllowAnyHeader();
});


//app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();
