using Microsoft.AspNetCore.Mvc;
using Whatsapp_Chatbot.Services;
using Whatsapp_Chatbot.Dtos;

namespace Whatsapp_Chatbot.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AiController : ControllerBase
    {
        private readonly IAiService _aiService;

        public AiController(IAiService aiService)
        {
            _aiService = aiService;
        }

        [HttpPost("ask")]
        public async Task<IActionResult> AskAi([FromBody] AskAiDto request)
        {
            if (string.IsNullOrEmpty(request.Message))
            {
                return BadRequest("Message cannot be empty");
            }

            try
            {
                var response = await _aiService.AskGeminiAsync(request.Message);
                return Ok(new { response });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}
